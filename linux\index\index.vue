<template>
	<page-container :isShowNav="false" bgColorPage="#FAFAFA">
		<image
			:src="backgroundImage"
			:style="backgroundImageStyle"
			mode="aspectFill"
			class="w-100% h-360 fixed top-0 z-10"
		/>
		<custom-nav bg-color="unset" title="" :is-back="true"> </custom-nav>
		<view class="content-wrapper px-24">
			<view class="server-info sticky top-200 z-10">
				<view class="relative flex justify-between">
					<view class="flex flex-col">
						<view class="text-36 font-bold">宝塔面板-AAA-BBB</view>
						<view class="text-24 py-16">IP：************* | Opencloudos 9</view>
						<view class="text-24 flex items-center">
							<view class="w-16 h-16 bg-#20a50a rd-50%"></view>
							<text class="text-24 pl-16">持续运行3天</text>
						</view>
					</view>
					<image
						src="@/static/index/server-bg.png"
						mode="aspectFit"
						class="absolute -top-120 -right-80 w-460 h-460"
					></image>
				</view>
			</view>
			<view class="detail mt-68 z-1">
				<function-list
					title="功能"
					:function-list="basicFunctionList"
					:show-edit="true"
					:columns="5"
					@itemClick="() => {}"
					@editClick="() => {}"
				/>
				<function-list
					class="mt-24"
					title="插件"
					:function-list="pluginFunctionList"
					:show-edit="true"
					:columns="4"
					@itemClick="() => {}"
					@editClick="() => {}"
				/>
				<statusInfo title="系统状态监控" :show-toggle-text="true">
					<template #actions>
						<uv-button size="mini" type="primary" @click="refreshData">
							<uv-icon name="reload" size="16" color="white"></uv-icon>
						</uv-button>
						<uv-button size="mini" @click="showSettings">
							<uv-icon name="setting" size="16"></uv-icon>
						</uv-button>
					</template>

					<template #basic>
						<view class="basic-info">
							<view class="info-item">
								<text class="label">CPU</text>
								<view class="values">
									<text class="value">核心: 4</text>
									<text class="value">已用: 0.54%</text>
									<text class="value">空闲: 98.6%</text>
								</view>
							</view>
							<view class="progress-indicator">
								<text class="percentage">40%</text>
							</view>
						</view>
					</template>

					<template #details>
						<view class="details-info">
							<view class="detail-item">
								<text class="detail-label">详细监控数据</text>
								<view class="detail-content">
									<view class="monitor-grid">
										<view class="monitor-item">
											<text class="monitor-label">温度</text>
											<text class="monitor-value">45°C</text>
										</view>
										<view class="monitor-item">
											<text class="monitor-label">频率</text>
											<text class="monitor-value">2.4GHz</text>
										</view>
										<view class="monitor-item">
											<text class="monitor-label">进程</text>
											<text class="monitor-value">156</text>
										</view>
										<view class="monitor-item">
											<text class="monitor-label">线程</text>
											<text class="monitor-value">892</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</template>
				</statusInfo>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
	import { onReady, onUnload, onShow, onHide, onLoad } from '@dcloudio/uni-app';
	import CustomNav from '@/components/customNav/index.vue';
	import PageContainer from '@/components/PageContainer/index.vue';
	import BtButton from '@/components/BtButton/index.vue';
	import { useConfigStore } from '@/store/modules/config';
	import ECharts from '@/components/ECharts/index.vue';
	import { $t } from '@/locale/index.js';
	import bgLight from '@/static/index/bg-light.png';
	import bgDark from '@/static/index/bg-dark.png';
	import FunctionList from './functionList.vue';
	import StatusInfo from './statusInfo.vue';
	const basicFunctionList = ref([
		{
			label: '网站',
			image: '/static/index/web_site.png',
		},
		{
			label: '数据库',
			image: '/static/index/database.png',
		},
		{
			label: '系统防火墙',
			image: '/static/index/firewall.png',
		},
		{
			label: '终端',
			image: '/static/index/terminal.png',
		},
		{
			label: '安全风险',
			image: '/static/index/security_risk.png',
		},
		{
			label: '监控',
			image: '/static/index/system_monitor.png',
		},
		{
			label: 'SSH管理',
			image: '/static/index/ssh_manager.png',
		},
		{
			label: '文件管理',
			image: '/static/index/file_manager.png',
		},
		{
			label: '计划任务',
			image: '/static/index/cron_task.png',
		},
		{
			label: '日志',
			image: '/static/index/log_manager.png',
		}
	]);

	const pluginFunctionList = ref([
		{
			label: '站点监控',
			image: '/static/index/web_monitor.png',
		},
		{
			label: '系统加固',
			image: '/static/index/os_hardening.png',
		},
		{
			label: 'nginx防火墙',
			image: '/static/index/nginx_waf.png',
		},
		{
			label: '防篡改',
			image: '/static/index/tamper_protection.png',
		}
	]);

	const backgroundImage = ref(bgLight);
	const backgroundImageStyle = ref({
		backgroundImage: `url(${backgroundImage.value})`,
		backgroundSize: 'cover',
		backgroundPosition: 'center',
	});
</script>
<style lang="scss" scoped></style>
