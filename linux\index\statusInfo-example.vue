<template>
	<page-container title="可展开布局组件示例">
		<view class="example-container">
			<!-- 示例1：基本用法 -->
			<view class="example-section">
				<text class="section-title">基本用法</text>
				<statusInfo>
					<template #basic>
						<view class="basic-info">
							<view class="info-item">
								<text class="label">负载</text>
								<view class="values">
									<text class="value">1分钟: 0.02</text>
									<text class="value">5分钟: 0.01</text>
									<text class="value">15分钟: 0.08</text>
								</view>
							</view>
							<view class="progress-indicator">
								<text class="percentage">70%</text>
							</view>
						</view>
					</template>
					
					<template #details>
						<view class="details-info">
							<view class="detail-item">
								<text class="detail-label">CPU详情</text>
								<view class="detail-content">
									<view class="cpu-info">
										<text>核心: 4</text>
										<text>已用: 0.54%</text>
										<text>空闲: 98.6%</text>
									</view>
									<view class="cpu-chart">
										<!-- 这里可以放置图表组件 -->
										<view class="chart-placeholder">
											<text>CPU使用率图表</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</template>
				</statusInfo>
			</view>

			<!-- 示例2：默认展开 -->
			<view class="example-section">
				<text class="section-title">默认展开</text>
				<statusInfo :default-expanded="true">
					<template #basic>
						<view class="basic-info">
							<view class="info-item">
								<text class="label">内存</text>
								<view class="values">
									<text class="value">总计: 8GB</text>
									<text class="value">已用: 3.2GB</text>
									<text class="value">可用: 4.8GB</text>
								</view>
							</view>
							<view class="progress-indicator">
								<text class="percentage">40%</text>
							</view>
						</view>
					</template>
					
					<template #details>
						<view class="details-info">
							<view class="detail-item">
								<text class="detail-label">内存详情</text>
								<view class="detail-content">
									<view class="memory-breakdown">
										<view class="memory-item">
											<text class="memory-type">系统</text>
											<text class="memory-usage">1.2GB</text>
										</view>
										<view class="memory-item">
											<text class="memory-type">应用</text>
											<text class="memory-usage">2.0GB</text>
										</view>
										<view class="memory-item">
											<text class="memory-type">缓存</text>
											<text class="memory-usage">0.8GB</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</template>
				</statusInfo>
			</view>

			<!-- 示例3：外部控制展开状态 -->
			<view class="example-section">
				<text class="section-title">外部控制</text>
				<view class="control-buttons">
					<uv-button 
						@click="diskExpanded = !diskExpanded"
						:type="diskExpanded ? 'primary' : 'default'"
						size="small"
					>
						{{ diskExpanded ? '收起' : '展开' }}磁盘信息
					</uv-button>
				</view>
				
				<statusInfo v-model="diskExpanded" @toggle="onDiskToggle">
					<template #basic>
						<view class="basic-info">
							<view class="info-item">
								<text class="label">磁盘</text>
								<view class="values">
									<text class="value">总计: 500GB</text>
									<text class="value">已用: 120GB</text>
									<text class="value">可用: 380GB</text>
								</view>
							</view>
							<view class="progress-indicator">
								<text class="percentage">24%</text>
							</view>
						</view>
					</template>
					
					<template #details>
						<view class="details-info">
							<view class="detail-item">
								<text class="detail-label">磁盘分区</text>
								<view class="detail-content">
									<view class="disk-partitions">
										<view class="partition-item">
											<text class="partition-name">/</text>
											<text class="partition-size">50GB / 100GB</text>
											<text class="partition-usage">50%</text>
										</view>
										<view class="partition-item">
											<text class="partition-name">/home</text>
											<text class="partition-size">70GB / 400GB</text>
											<text class="partition-usage">17.5%</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</template>
				</statusInfo>
			</view>

			<!-- 示例4：禁用展开 -->
			<view class="example-section">
				<text class="section-title">禁用展开</text>
				<statusInfo :disabled="true">
					<template #basic>
						<view class="basic-info">
							<view class="info-item">
								<text class="label">网络</text>
								<view class="values">
									<text class="value">上传: 1.2MB/s</text>
									<text class="value">下载: 5.8MB/s</text>
								</view>
							</view>
							<view class="status-indicator">
								<text class="status-text">正常</text>
							</view>
						</view>
					</template>
					
					<template #details>
						<view class="details-info">
							<text>此组件已禁用展开功能</text>
						</view>
					</template>
				</statusInfo>
			</view>
		</view>
	</page-container>
</template>

<script setup>
import { ref } from 'vue';
import PageContainer from '@/components/pageContainer/index.vue';
import statusInfo from './statusInfo.vue';

// 外部控制的展开状态
const diskExpanded = ref(false);

// 监听展开状态变化
const onDiskToggle = (expanded) => {
	console.log('磁盘信息展开状态:', expanded);
};
</script>

<style lang="scss" scoped>
.example-container {
	padding: 32rpx;
}

.example-section {
	margin-bottom: 48rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: var(--text-color-primary);
	margin-bottom: 24rpx;
	display: block;
}

.control-buttons {
	margin-bottom: 24rpx;
}

// 基本信息样式
.basic-info {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
}

.info-item {
	flex: 1;
}

.label {
	font-size: 28rpx;
	font-weight: 600;
	color: var(--text-color-primary);
	margin-bottom: 12rpx;
	display: block;
}

.values {
	display: flex;
	gap: 24rpx;
}

.value {
	font-size: 24rpx;
	color: var(--text-color-secondary);
}

.progress-indicator, .status-indicator {
	margin-left: 24rpx;
}

.percentage {
	background: #20a50a;
	color: white;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 600;
}

.status-text {
	background: #20a50a;
	color: white;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 600;
}

// 详情信息样式
.details-info {
	padding-top: 16rpx;
}

.detail-item {
	margin-bottom: 32rpx;
}

.detail-label {
	font-size: 26rpx;
	font-weight: 600;
	color: var(--text-color-primary);
	margin-bottom: 16rpx;
	display: block;
}

.detail-content {
	background: rgba(247, 247, 247, 0.5);
	padding: 24rpx;
	border-radius: 12rpx;
}

// CPU信息样式
.cpu-info {
	display: flex;
	gap: 32rpx;
	margin-bottom: 24rpx;
	
	text {
		font-size: 24rpx;
		color: var(--text-color-secondary);
	}
}

.chart-placeholder {
	height: 200rpx;
	background: #f5f5f5;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	
	text {
		color: #999;
		font-size: 24rpx;
	}
}

// 内存信息样式
.memory-breakdown {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.memory-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1rpx solid #eee;
	
	&:last-child {
		border-bottom: none;
	}
}

.memory-type {
	font-size: 24rpx;
	color: var(--text-color-primary);
}

.memory-usage {
	font-size: 24rpx;
	color: var(--text-color-secondary);
	font-weight: 600;
}

// 磁盘信息样式
.disk-partitions {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.partition-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx;
	background: white;
	border-radius: 8rpx;
	border: 1rpx solid #eee;
}

.partition-name {
	font-size: 24rpx;
	color: var(--text-color-primary);
	font-weight: 600;
	min-width: 80rpx;
}

.partition-size {
	font-size: 22rpx;
	color: var(--text-color-secondary);
	flex: 1;
	text-align: center;
}

.partition-usage {
	font-size: 22rpx;
	color: #20a50a;
	font-weight: 600;
	min-width: 60rpx;
	text-align: right;
}
</style>
