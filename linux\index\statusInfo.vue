<template>
	<view class="expandable-layout" :class="{ 'expandable-layout--disabled': disabled }">
		<!-- 基本信息区域 - 始终显示 -->
		<view class="expandable-layout__basic" @click="handleToggle">
			<view class="expandable-layout__basic-content">
				<slot name="basic"></slot>
			</view>

			<!-- 展开/收起按钮 -->
			<view v-if="!disabled" class="expandable-layout__toggle-btn">
				<uv-icon
					:name="isExpanded ? 'arrow-up' : 'arrow-down'"
					size="20"
					color="#757575"
					class="toggle-icon"
				></uv-icon>
			</view>
		</view>

		<!-- 详情信息区域 - 可展开 -->
		<uv-transition :show="isExpanded" mode="slide-down" :duration="300">
			<view class="expandable-layout__details">
				<view class="expandable-layout__details-content">
					<slot name="details"></slot>
				</view>
			</view>
		</uv-transition>
	</view>
</template>

<script setup>
	import { ref, watch } from 'vue';

	// Props 定义
	const props = defineProps({
		// 是否默认展开
		defaultExpanded: {
			type: Boolean,
			default: false,
		},
		// 是否禁用展开功能
		disabled: {
			type: Boolean,
			default: false,
		},
		// 外部控制展开状态
		modelValue: {
			type: Boolean,
			default: undefined,
		},
	});

	// Emits 定义
	const emit = defineEmits(['update:modelValue', 'toggle']);

	// 内部展开状态
	const isExpanded = ref(props.defaultExpanded);

	// 监听外部传入的 modelValue
	watch(
		() => props.modelValue,
		(newVal) => {
			if (newVal !== undefined) {
				isExpanded.value = newVal;
			}
		},
		{ immediate: true },
	);

	// 监听内部状态变化，同步到外部
	watch(isExpanded, (newVal) => {
		emit('update:modelValue', newVal);
		emit('toggle', newVal);
	});

	// 切换展开状态
	const handleToggle = () => {
		if (props.disabled) return;

		// 如果外部有传入 modelValue，则通过 emit 通知外部更新
		if (props.modelValue !== undefined) {
			emit('update:modelValue', !props.modelValue);
		} else {
			// 否则直接更新内部状态
			isExpanded.value = !isExpanded.value;
		}
	};
</script>

<style lang="scss" scoped>
	.expandable-layout {
		background: var(--bg-color, #ffffff);
		border-radius: 16rpx;
		overflow: hidden;
		border: 1rpx solid var(--border-color, #f0f0f0);
		transition: all 0.3s ease;

		&--disabled {
			.expandable-layout__basic {
				cursor: default;
			}

			.expandable-layout__toggle-btn {
				opacity: 0.3;
			}
		}
	}

	.expandable-layout__basic {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx;
		background: rgba(247, 247, 247, 1);
		cursor: pointer;
		transition: background-color 0.2s ease;

		&:hover:not(.expandable-layout--disabled &) {
			background: rgba(240, 240, 240, 1);
		}

		&:active:not(.expandable-layout--disabled &) {
			background: rgba(235, 235, 235, 1);
		}
	}

	.expandable-layout__basic-content {
		flex: 1;
		min-width: 0; // 防止内容溢出
	}

	.expandable-layout__toggle-btn {
		margin-left: 16rpx;
		padding: 8rpx;
		border-radius: 50%;
		transition: all 0.2s ease;

		&:hover {
			background: rgba(0, 0, 0, 0.05);
		}
	}

	.toggle-icon {
		transition: transform 0.3s ease;
	}

	.expandable-layout__details {
		border-top: 1rpx solid var(--border-color, #f0f0f0);
		background: var(--bg-color, #ffffff);
	}

	.expandable-layout__details-content {
		padding: 24rpx;
	}

	// 深色模式适配
	@media (prefers-color-scheme: dark) {
		.expandable-layout {
			background: var(--bg-color, #1a1a1a);
			border-color: var(--border-color, #333333);
		}

		.expandable-layout__basic {
			background: rgba(40, 40, 40, 1);

			&:hover:not(.expandable-layout--disabled &) {
				background: rgba(50, 50, 50, 1);
			}

			&:active:not(.expandable-layout--disabled &) {
				background: rgba(60, 60, 60, 1);
			}
		}

		.expandable-layout__details {
			background: var(--bg-color, #1a1a1a);
			border-color: var(--border-color, #333333);
		}

		.expandable-layout__toggle-btn:hover {
			background: rgba(255, 255, 255, 0.05);
		}
	}
</style>
