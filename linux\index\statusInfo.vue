<template>
	<view class="expandable-layout" :class="{ 'expandable-layout--disabled': disabled }">
		<!-- 标题区域 - 可选 -->
		<view v-if="title || $slots.title || $slots.actions" class="expandable-layout__header">
			<view class="expandable-layout__header-content">
				<!-- 标题内容 -->
				<view class="expandable-layout__title">
					<slot name="title">
						<text v-if="title" class="title-text">{{ title }}</text>
					</slot>
				</view>

				<!-- 操作按钮区域 -->
				<view v-if="$slots.actions" class="expandable-layout__actions">
					<slot name="actions"></slot>
				</view>
			</view>
		</view>

		<!-- 基本信息区域 - 始终显示 -->
		<view class="expandable-layout__basic" @click="handleToggle">
			<view class="expandable-layout__basic-content">
				<slot name="basic"></slot>
			</view>

			<!-- 展开/收起按钮 -->
			<view v-if="!disabled" class="expandable-layout__toggle-btn" @click.stop="handleToggle">
				<uv-icon
					:name="isExpanded ? expandedIcon : collapsedIcon"
					size="20"
					color="#757575"
					class="toggle-icon"
				></uv-icon>
				<text v-if="showToggleText" class="toggle-text">
					{{ isExpanded ? expandedText : collapsedText }}
				</text>
			</view>
		</view>

		<!-- 详情信息区域 - 可展开 -->
		<uv-transition :show="isExpanded" mode="slide-bottom" :duration="300">
			<view class="expandable-layout__details">
				<view class="expandable-layout__details-content">
					<slot name="details"></slot>
				</view>
			</view>
		</uv-transition>
	</view>
</template>

<script setup>
	import { ref, watch } from 'vue';

	// Props 定义
	const props = defineProps({
		// 标题文本
		title: {
			type: String,
			default: '',
		},
		// 是否默认展开
		defaultExpanded: {
			type: Boolean,
			default: false,
		},
		// 是否禁用展开功能
		disabled: {
			type: Boolean,
			default: false,
		},
		// 外部控制展开状态
		modelValue: {
			type: Boolean,
			default: undefined,
		},
		// 展开状态图标
		expandedIcon: {
			type: String,
			default: 'arrow-up',
		},
		// 收起状态图标
		collapsedIcon: {
			type: String,
			default: 'arrow-down',
		},
		// 展开状态文本
		expandedText: {
			type: String,
			default: '收起',
		},
		// 收起状态文本
		collapsedText: {
			type: String,
			default: '详情',
		},
		// 是否显示切换按钮文本
		showToggleText: {
			type: Boolean,
			default: false,
		},
	});

	// Emits 定义
	const emit = defineEmits(['update:modelValue', 'toggle']);

	// 内部展开状态
	const isExpanded = ref(props.defaultExpanded);

	// 监听外部传入的 modelValue
	watch(
		() => props.modelValue,
		(newVal) => {
			if (newVal !== undefined) {
				isExpanded.value = newVal;
			}
		},
		{ immediate: true },
	);

	// 监听内部状态变化，同步到外部
	watch(isExpanded, (newVal) => {
		emit('update:modelValue', newVal);
		emit('toggle', newVal);
	});

	// 切换展开状态
	const handleToggle = () => {
		if (props.disabled) return;

		// 如果外部有传入 modelValue，则通过 emit 通知外部更新
		if (props.modelValue !== undefined) {
			emit('update:modelValue', !props.modelValue);
		} else {
			// 否则直接更新内部状态
			isExpanded.value = !isExpanded.value;
		}
	};
</script>

<style lang="scss" scoped>
	.expandable-layout {
		background: var(--bg-color, #ffffff);
		border-radius: 16rpx;
		overflow: hidden;
		border: 1rpx solid var(--border-color, #f0f0f0);
		transition: all 0.3s ease;

		&--disabled {
			.expandable-layout__basic {
				cursor: default;
			}

			.expandable-layout__toggle-btn {
				opacity: 0.3;
			}
		}
	}

	// 标题区域样式
	.expandable-layout__header {
		border-bottom: 1rpx solid var(--border-color, #f0f0f0);
		background: var(--bg-color, #ffffff);
	}

	.expandable-layout__header-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx;
	}

	.expandable-layout__title {
		flex: 1;
		min-width: 0;
	}

	.title-text {
		font-size: 30rpx;
		font-weight: 600;
		color: var(--text-color-primary);
		line-height: 1.4;
	}

	.expandable-layout__actions {
		margin-left: 24rpx;
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.expandable-layout__basic {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx;
		background: rgba(247, 247, 247, 1);
		cursor: pointer;
		transition: background-color 0.2s ease;

		&:hover:not(.expandable-layout--disabled &) {
			background: rgba(240, 240, 240, 1);
		}

		&:active:not(.expandable-layout--disabled &) {
			background: rgba(235, 235, 235, 1);
		}
	}

	.expandable-layout__basic-content {
		flex: 1;
		min-width: 0; // 防止内容溢出
	}

	.expandable-layout__toggle-btn {
		margin-left: 16rpx;
		padding: 8rpx 12rpx;
		border-radius: 20rpx;
		transition: all 0.2s ease;
		display: flex;
		align-items: center;
		gap: 8rpx;
		cursor: pointer;

		&:hover {
			background: rgba(0, 0, 0, 0.05);
		}

		&:active {
			background: rgba(0, 0, 0, 0.1);
		}
	}

	.toggle-icon {
		transition: transform 0.3s ease;
	}

	.toggle-text {
		font-size: 24rpx;
		color: #757575;
		font-weight: 500;
		white-space: nowrap;
	}

	.expandable-layout__details {
		border-top: 1rpx solid var(--border-color, #f0f0f0);
		background: var(--bg-color, #ffffff);
	}

	.expandable-layout__details-content {
		padding: 24rpx;
	}

	// 深色模式适配
	@media (prefers-color-scheme: dark) {
		.expandable-layout {
			background: var(--bg-color, #1a1a1a);
			border-color: var(--border-color, #333333);
		}

		.expandable-layout__header {
			background: var(--bg-color, #1a1a1a);
			border-color: var(--border-color, #333333);
		}

		.title-text {
			color: var(--text-color-primary, #ffffff);
		}

		.expandable-layout__basic {
			background: rgba(40, 40, 40, 1);

			&:hover:not(.expandable-layout--disabled &) {
				background: rgba(50, 50, 50, 1);
			}

			&:active:not(.expandable-layout--disabled &) {
				background: rgba(60, 60, 60, 1);
			}
		}

		.expandable-layout__details {
			background: var(--bg-color, #1a1a1a);
			border-color: var(--border-color, #333333);
		}

		.expandable-layout__toggle-btn {
			&:hover {
				background: rgba(255, 255, 255, 0.05);
			}

			&:active {
				background: rgba(255, 255, 255, 0.1);
			}
		}

		.toggle-text {
			color: #b0b0b0;
		}
	}
</style>
