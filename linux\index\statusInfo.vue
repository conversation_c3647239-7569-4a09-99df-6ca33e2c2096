<template>
	<page-container title="系统状态信息" ref="pageContainer">
		<view class="status-info-page">
			<!-- 示例1: 网格布局 - 系统监控概览 -->
			<view class="section">
				<text class="section-title">系统监控概览（网格布局）</text>
				<StatusLayout
					mode="grid"
					:grid-columns="3"
					:grid-rows="2"
					:animated="true"
					custom-style="margin-bottom: 20rpx;"
				>
					<template #grid-items>
						<!-- 负载指标 - 第1列，跨2行 -->
						<StatusLayout
							mode="column"
							title="负载"
							subtitle="70%"
							:loading="loadingStates.load"
							:offline="offlineStates.load"
							custom-style="grid-column: 1; grid-row: 1 / 3;"
						>
							<template #status>
								<text class="status-badge status-normal">正常</text>
							</template>
							<template #chart>
								<MetricProgressBar
									mode="vertical"
									:value="70"
									height="180rpx"
									thickness="100rpx"
									:animated="true"
								/>
							</template>
						</StatusLayout>

						<!-- CPU指标 - 第2列第1行 -->
						<StatusLayout
							mode="chart"
							title="CPU"
							subtitle="4核"
							:loading="loadingStates.cpu"
							:offline="offlineStates.cpu"
							chart-height="110rpx"
							custom-style="grid-column: 2; grid-row: 1;"
						>
							<template #chart>
								<ECharts
									canvas-id="demo-cpu-chart"
									chart-type="gauge"
									:chart-data="cpuChartData"
									:height="110"
								/>
							</template>
						</StatusLayout>

						<!-- 内存指标 - 第3列第1行 -->
						<StatusLayout
							mode="chart"
							title="内存"
							subtitle="16GB"
							:loading="loadingStates.memory"
							:offline="offlineStates.memory"
							chart-height="110rpx"
							custom-style="grid-column: 3; grid-row: 1;"
						>
							<template #chart>
								<ECharts
									canvas-id="demo-memory-chart"
									chart-type="gauge"
									:chart-data="memoryChartData"
									:height="110"
								/>
							</template>
						</StatusLayout>

						<!-- 磁盘指标 - 第2-3列第2行 -->
						<StatusLayout
							mode="bar"
							title="磁盘(/)"
							:loading="loadingStates.disk"
							:offline="offlineStates.disk"
							custom-style="grid-column: 2 / 4; grid-row: 2;"
						>
							<template #status>
								<text class="status-badge status-normal">40%</text>
							</template>
							<template #progress>
								<MetricProgressBar
									mode="horizontal"
									:value="40"
									width="100%"
									thickness="16rpx"
									:animated="true"
								/>
							</template>
							<template #details>
								<text>总计: 80GB</text>
								<text>已用: 32GB</text>
								<text>空闲: 48GB</text>
							</template>
						</StatusLayout>
					</template>
				</StatusLayout>
			</view>

			<!-- 示例2: 卡片布局 - 独立指标卡片 -->
			<view class="section">
				<text class="section-title">独立指标卡片（卡片布局）</text>
				<view class="cards-container">
					<!-- CPU 卡片 -->
					<StatusLayout
						mode="card"
						title="CPU使用率"
						subtitle="Intel Xeon Platinum"
						:loading="loadingStates.cpu"
						:offline="offlineStates.cpu"
						custom-style="flex: 1; margin-right: 10rpx;"
					>
						<template #action>
							<text class="status-badge status-normal">40%</text>
						</template>
						<template #content>
							<ECharts
								canvas-id="demo-cpu-card-chart"
								chart-type="gauge"
								:chart-data="cpuChartData"
								:height="120"
							/>
						</template>
						<template #footer>
							<text class="detail-text">4核心 | 8线程</text>
						</template>
					</StatusLayout>

					<!-- 内存卡片 -->
					<StatusLayout
						mode="card"
						title="内存使用率"
						subtitle="DDR4 3200MHz"
						:loading="loadingStates.memory"
						:offline="offlineStates.memory"
						custom-style="flex: 1; margin-left: 10rpx;"
					>
						<template #action>
							<text class="status-badge status-medium">65%</text>
						</template>
						<template #content>
							<ECharts
								canvas-id="demo-memory-card-chart"
								chart-type="gauge"
								:chart-data="memoryChartData"
								:height="120"
							/>
						</template>
						<template #footer>
							<text class="detail-text">已用: 10.4GB / 16GB</text>
						</template>
					</StatusLayout>
				</view>
			</view>

			<!-- 示例3: 条形布局 - 磁盘使用情况 -->
			<view class="section">
				<text class="section-title">磁盘使用情况（条形布局）</text>
				<view class="bars-container">
					<StatusLayout
						mode="bar"
						title="系统盘 (/)"
						subtitle="SSD 固态硬盘"
						:loading="loadingStates.disk"
						:offline="offlineStates.disk"
						custom-style="margin-bottom: 16rpx;"
					>
						<template #status>
							<text class="status-badge status-normal">40%</text>
						</template>
						<template #progress>
							<MetricProgressBar
								mode="horizontal"
								:value="40"
								width="100%"
								thickness="20rpx"
								:animated="true"
							/>
						</template>
						<template #details>
							<text>已用: 32GB</text>
							<text>总计: 80GB</text>
						</template>
					</StatusLayout>

					<StatusLayout
						mode="bar"
						title="数据盘 (/data)"
						subtitle="HDD 机械硬盘"
						:loading="loadingStates.disk"
						:offline="offlineStates.disk"
						custom-style="margin-bottom: 16rpx;"
					>
						<template #status>
							<text class="status-badge status-high">85%</text>
						</template>
						<template #progress>
							<MetricProgressBar
								mode="horizontal"
								:value="85"
								width="100%"
								thickness="20rpx"
								:animated="true"
							/>
						</template>
						<template #details>
							<text>已用: 850GB</text>
							<text>总计: 1TB</text>
						</template>
					</StatusLayout>
				</view>
			</view>

			<!-- 示例4: 图表布局 - 网络流量 -->
			<view class="section">
				<text class="section-title">网络流量（图表布局）</text>
				<StatusLayout
					mode="chart"
					title="网络流量监控"
					subtitle="实时数据"
					:loading="loadingStates.network"
					:offline="offlineStates.network"
					chart-height="300rpx"
				>
					<template #action>
						<uv-button size="mini" type="primary" @click="refreshNetworkData">刷新</uv-button>
					</template>
					<template #chart>
						<ECharts
							canvas-id="demo-network-chart"
							chart-type="line"
							:chart-data="networkChartData"
							:height="300"
						/>
					</template>
					<template #details>
						<view class="network-stats">
							<view class="stat-item">
								<text class="stat-label">↓ 下载:</text>
								<text class="stat-value">1.56 KB/s</text>
							</view>
							<view class="stat-item">
								<text class="stat-label">↑ 上传:</text>
								<text class="stat-value">24.3 KB/s</text>
							</view>
						</view>
					</template>
				</StatusLayout>
			</view>

			<!-- 控制按钮区域 -->
			<view class="control-section">
				<text class="section-title">状态控制</text>
				<view class="control-buttons">
					<uv-button type="primary" @click="toggleLoading">切换加载状态</uv-button>
					<uv-button type="warning" @click="toggleOffline">切换离线状态</uv-button>
					<uv-button type="success" @click="refreshAllData">刷新所有数据</uv-button>
				</view>
			</view>
		</view>
	</page-container>
</template>

<script setup>
	import { ref, reactive, onMounted } from 'vue';
	import PageContainer from '@/components/PageContainer/index.vue';
	import StatusLayout from '@/components/StatusLayout/index.vue';
	import MetricProgressBar from '@/components/MetricProgressBar/index.vue';
	import ECharts from '@/components/ECharts/index.vue';

	// 页面容器引用
	const pageContainer = ref(null);

	// 加载状态管理
	const loadingStates = reactive({
		load: false,
		cpu: false,
		memory: false,
		disk: false,
		network: false,
	});

	// 离线状态管理
	const offlineStates = reactive({
		load: false,
		cpu: false,
		memory: false,
		disk: false,
		network: false,
	});

	// CPU 图表数据
	const cpuChartData = ref({
		value: 40,
		name: 'CPU使用率',
		unit: '%',
		color: '#20a50a',
	});

	// 内存图表数据
	const memoryChartData = ref({
		value: 65,
		name: '内存使用率',
		unit: '%',
		color: '#ff9800',
	});

	// 网络图表数据
	const networkChartData = ref({
		xAxis: ['00:00', '00:05', '00:10', '00:15', '00:20', '00:25', '00:30'],
		series: [
			{
				name: '下载速度',
				data: [1.2, 1.5, 1.8, 1.6, 1.4, 1.7, 1.56],
				color: '#20a50a',
			},
			{
				name: '上传速度',
				data: [20.1, 22.3, 25.6, 24.8, 23.2, 24.9, 24.3],
				color: '#ff9800',
			},
		],
	});

	// 切换加载状态
	const toggleLoading = () => {
		const keys = Object.keys(loadingStates);
		keys.forEach((key) => {
			loadingStates[key] = !loadingStates[key];
		});

		// 模拟加载完成
		if (loadingStates.cpu) {
			setTimeout(() => {
				keys.forEach((key) => {
					loadingStates[key] = false;
				});
				pageContainer.value?.notify?.success('数据加载完成');
			}, 2000);
		}
	};

	// 切换离线状态
	const toggleOffline = () => {
		const keys = Object.keys(offlineStates);
		keys.forEach((key) => {
			offlineStates[key] = !offlineStates[key];
		});

		if (offlineStates.cpu) {
			pageContainer.value?.notify?.warning('模拟离线状态');
		} else {
			pageContainer.value?.notify?.success('恢复在线状态');
		}
	};

	// 刷新网络数据
	const refreshNetworkData = () => {
		loadingStates.network = true;

		setTimeout(() => {
			// 模拟数据更新
			networkChartData.value = {
				...networkChartData.value,
				series: networkChartData.value.series.map((series) => ({
					...series,
					data: series.data.map(() => Math.random() * 30 + 10),
				})),
			};

			loadingStates.network = false;
			pageContainer.value?.notify?.success('网络数据已刷新');
		}, 1000);
	};

	// 刷新所有数据
	const refreshAllData = () => {
		const keys = Object.keys(loadingStates);
		keys.forEach((key) => {
			loadingStates[key] = true;
		});

		setTimeout(() => {
			// 模拟数据更新
			cpuChartData.value.value = Math.floor(Math.random() * 100);
			memoryChartData.value.value = Math.floor(Math.random() * 100);

			keys.forEach((key) => {
				loadingStates[key] = false;
			});

			pageContainer.value?.notify?.success('所有数据已刷新');
		}, 2000);
	};

	// 组件挂载时初始化
	onMounted(() => {
		console.log('StatusInfo 页面已加载');
	});
</script>

<style lang="scss" scoped>
	.status-info-page {
		padding: 20rpx;
		background: linear-gradient(180deg, #e2ecee 0%, #ffffff 100%);
		min-height: 100vh;
	}

	.section {
		margin-bottom: 40rpx;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
		margin-bottom: 20rpx;
		display: block;
	}

	/* ==================== 卡片容器样式 ==================== */
	.cards-container {
		display: flex;
		gap: 20rpx;
	}

	/* ==================== 条形容器样式 ==================== */
	.bars-container {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
	}

	/* ==================== 状态徽章样式 ==================== */
	.status-badge {
		font-size: 20rpx;
		padding: 4rpx 8rpx;
		border-radius: 8rpx;
		font-weight: 500;
		white-space: nowrap;
	}

	.status-normal {
		color: rgba(32, 165, 58, 1);
		background: rgba(32, 165, 58, 0.1);
	}

	.status-medium {
		color: rgba(255, 193, 7, 1);
		background: rgba(255, 193, 7, 0.1);
	}

	.status-high {
		color: rgba(255, 152, 0, 1);
		background: rgba(255, 152, 0, 0.1);
	}

	.status-critical {
		color: rgba(211, 47, 47, 1);
		background: rgba(211, 47, 47, 0.1);
	}

	/* ==================== 详情文本样式 ==================== */
	.detail-text {
		font-size: 22rpx;
		color: #666;
		text-align: center;
	}

	/* ==================== 网络统计样式 ==================== */
	.network-stats {
		display: flex;
		justify-content: space-around;
		align-items: center;
		width: 100%;
	}

	.stat-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 4rpx;
	}

	.stat-label {
		font-size: 20rpx;
		color: #666;
	}

	.stat-value {
		font-size: 24rpx;
		font-weight: 600;
		color: #333;
	}

	/* ==================== 控制区域样式 ==================== */
	.control-section {
		margin-top: 60rpx;
		padding: 30rpx;
		background: rgba(255, 255, 255, 0.8);
		border-radius: 20rpx;
		backdrop-filter: blur(10px);
	}

	.control-buttons {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	/* ==================== 响应式设计 ==================== */
	@media (max-width: 750rpx) {
		.cards-container {
			flex-direction: column;
		}

		.control-buttons {
			gap: 16rpx;
		}

		.section-title {
			font-size: 30rpx;
		}
	}

	/* ==================== 动画效果 ==================== */
	.status-info-page {
		animation: pageSlideIn 0.5s ease;
	}

	@keyframes pageSlideIn {
		from {
			opacity: 0;
			transform: translateY(30rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
</style>
